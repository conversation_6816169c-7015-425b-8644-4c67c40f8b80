import UIKit
import Flutter
import CoreMotion

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
    private let CHANNEL = "compass_stream"
    private let motionManager = CMMotionManager()

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        let controller = window?.rootViewController as! FlutterViewController
        let channel = FlutterEventChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)

        channel.setStreamHandler(CompassStreamHandler(motionManager: motionManager))
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    override func applicationWillTerminate(_ application: UIApplication) {
        motionManager.stopMagnetometerUpdates()
        super.applicationWillTerminate(application)
    }
}

class CompassStreamHandler: NSObject, FlutterStreamHandler {
    private let motionManager: CMMotionManager
    private var filteredAngle: Double = 0.0
    private let alpha: Double = 0.33

    init(motionManager: CMMotionManager) {
        self.motionManager = motionManager
        super.init()
    }

    func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        if motionManager.isMagnetometerAvailable {
            if !motionManager.isMagnetometerActive {
                motionManager.magnetometerUpdateInterval = 0.1
                motionManager.startMagnetometerUpdates(to: OperationQueue.main) { [weak self] (data, error) in
                    guard let self = self else { return }
                    if let error = error {
                        print("Magnetometer error: \(error.localizedDescription)")
                        events(FlutterError(code: "SENSOR_ERROR", message: error.localizedDescription, details: nil))
                        return
                    }
                    if let data = data {
                        var direction = atan2(data.magneticField.y, data.magneticField.x) * 180 / .pi
                        let newAngle = direction >= 0 ? direction : direction + 360

                        var deltaAngle = newAngle - self.filteredAngle
                        if deltaAngle > 180 { deltaAngle -= 360 }
                        else if deltaAngle < -180 { deltaAngle += 360 }

                        self.filteredAngle += self.alpha * deltaAngle

                        if self.filteredAngle >= 360 { self.filteredAngle -= 360 }
                        else if self.filteredAngle < 0 { self.filteredAngle += 360 }

                        events(self.filteredAngle)
                    }
                }
            }
        } else {
            events(FlutterError(code: "UNAVAILABLE", message: "Magnetometer not available", details: nil))
        }
        return nil
    }

    func onCancel(withArguments arguments: Any?) -> FlutterError? {
        motionManager.stopMagnetometerUpdates()
        return nil
    }
}